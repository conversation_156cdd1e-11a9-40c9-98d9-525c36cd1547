import React from 'react';
import { Flex } from 'theme-ui';
import { Field } from 'formik';
import { useFormikContext } from 'formik';

const CustomTargetRangeInput = () => {
  const formikContext = useFormikContext();

  return (
    <Flex>
      <Field type="number" />
      <input type="number" />
      <input type="number" />
      <input type="number" />
    </Flex>
  );
};

export default CustomTargetRangeInput;
