/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.patient-data {
  margin-bottom: @spacing-large;
  position: relative;
}

@mobile-breakpoint: 768px;

// Subnav
// ====================================

@patient-data-subnav-vertical-padding: @spacing-small;
@patient-data-subnav-horizontal-padding: @spacing-medium;

@patient-data-subnav-breakpoint: 1080px;

.patient-data-subnav-inner {
  color: @blue-primary;
  background-color: @blue-gray-dark;
  padding: 0;
}

.patient-data-subnav {
  // Desktop
  min-height: 40px;

  display: grid;
  grid-template-columns: 5fr 2fr 5fr;

  // Tablet / Smaller Screen
  @media screen and (max-width: @patient-data-subnav-breakpoint) {
    grid-template-columns: 1fr;
  }

  // Mobile
  @media screen and (max-width: @mobile-breakpoint) {
    // Hide the entire bar if viewing settings chart or if no data
    &[data-chart-type="settings"],
    &[data-chart-type="no-data"] {
      display: none;
    }

    // Use dark text on light background if viewing data charts
    &[data-chart-type="daily"],
    &[data-chart-type="bgLog"],
    &[data-chart-type="basics"],
    &[data-chart-type="trends"] {
      background: @white-color;
      color: @blue-gray-dark;
      border-bottom: 1px solid #D9D9D9;
    }

    // Hide chart selection and print pdf button
    .patient-data-subnav-left,
    .patient-data-subnav-right {
      display: none;
    }

    // Hide date navigation icons
    a.patient-data-icon {
      display: none;
    }
  }
}

.patient-data-subnav-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  text-wrap: nowrap;
}

.patient-data-subnav-dates-daily {
  width: 180px;
}

.patient-data-subnav-dates-bgLog {
  width: 220px;
}

.patient-data-subnav-dates-trends {
  width: 220px;
}

.patient-data-subnav a {
  padding: @patient-data-subnav-vertical-padding @patient-data-subnav-horizontal-padding;

  font-size: 14px;

  &.patient-data-icon {
    padding: 0;
  }

  color: @blue-primary;
  text-decoration: none;

  transition: all .2s ease-out;

  &:active,
  &.patient-data-subnav-disabled {
    color: @gray;
    cursor: default;
  }

  // TODO: this should be removed when we support navigation across the settings history
  &.patient-data-subnav-hidden {
    display: none;
  }
}

.patient-data-subnav-tablink {
  &:first-child {
    border-radius: 3px 0 0 0;
  }

  &:last-child {
    border-radius: 0 3px 0 0;
  }

  &:hover,
  &:focus {
    background-color: @blue-gray-dark-active;
    text-decoration: none;
  }
}

.patient-data-subnav-active {
  background-color: @blue-gray-dark-active;
}

.patient-data-subnav-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.patient-data-subnav-left {
  display: flex;
  align-items: center;

  @media screen and (max-width: @patient-data-subnav-breakpoint) {
    justify-content: center;
  }
}

.patient-data-subnav-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  @media screen and (max-width: @patient-data-subnav-breakpoint) {
    justify-content: center;
  }
}

.patient-data-subnav-right-label {
  float: right;
}

// Loader
// ====================================
.patient-data > .loader {
  height: 540px;
  position: absolute;
  top: 0;
  width: 100%;
}

// Stats
// ====================================
.patient-data .Stats {
  clear: both;
  display: block;
  margin: 0;

  > div {
    margin: 0 0 8px;
    max-width: 100%;
  }
}

// Content
// ====================================

#tidelineMain.basics,
#tidelineMain.daily,
#tidelineMain.bgLog,
#tidelineMain.trends {
  .patient-data-content {
    display: block;

    @media screen and (max-width: @mobile-breakpoint) {
      display: none;
    }
  }
}

// Chart
// ====================================

.patient-data-chart {
  width: 100%;
  height: 590px;
}

.bgLog .patient-data-chart {
  height: 510px;
}

.patient-data-chart-growing {
  width: 100%;
}

.patient-data-chart-trends {
  width: 100%;
  height: 520px;
  overflow: hidden;
}

.patient-data-chart-trends-no-data {
  position: absolute;
  top: 230px;
  left: 30px;
  text-align: center;
  width: 100%;
}

@media print {
  .patient-data-chart {
    overflow-y: inherit;
  }

  i {
    display: none;
  }
}

// Messages
// ====================================

@patient-data-message-breakpoint: @screen-lg-min;

.patient-data-message {
  text-align: center;
  padding: 30px @spacing-small;
  margin-top: 80px;

  @media (min-width: @patient-data-message-breakpoint) {
    margin-left: auto;
    margin-right: auto;
    width: 480px;
  }

  & p {
    margin-top: @spacing-base;
    margin-bottom: @spacing-base;
  }
}

.patient-data-loading-message {
  margin-top: 0px;
  font-weight: bold;
}

.patient-data-message-no-data {
  text-align: center;
  margin-top: 80px;

  @media (min-width: @patient-data-message-breakpoint) {
    margin-left: auto;
    margin-right: auto;
    width: 625px;
  }

  h1 {
    font-size: 26px;
    font-weight: normal;
    margin-bottom: @spacing-large;
  }

  p {
    font-size: 18px;
  }

  .uploader-color-override {
    color: @purple-medium;

    &:hover,
    &:focus {
      color: @purple-uploader;
      text-decoration: none;
    }
  }
}

// Footer
// ====================================

@patient-data-footer-vertical-padding: @spacing-small;
@patient-data-footer-horizontal-padding: @spacing-small;

.patient-data-footer-inner {
  padding-left: 0;
  padding-right: 0;
  display: flex;
  justify-content: space-between;

  min-height: (2 * @spacing-small + @line-height-base);
}

.patient-data-footer a {
  color: @gray-darkest;
  display: inline-block;
  padding: @patient-data-footer-vertical-padding
    @patient-data-footer-horizontal-padding;

  text-decoration: none;

  &:hover,
  &:focus {
    color: @gray-darkest;
    text-decoration: underline;
  }

  &:active,
  &.patient-data-footer-disabled {
    color: @gray;
    cursor: default;
  }
}

.patient-data-footer-left {
  display: flex;
  justify-content: space-between;
  text-align: left;
  padding: @spacing-small;

  .toggle-container {
    padding: 0 0 0 15px;
  }
}

.patient-data-footer-right {
  text-align: right;
}

.footer-right-options {
  display: flex;
  justify-content: flex-end;

  label {
    padding: 15px 12px 0 10px;
    cursor: pointer;
  }
}

@media print {
  .patient-data-subnav-inner {
    color: gray;
    background-color: transparent;

    padding: @patient-data-subnav-vertical-padding
      @patient-data-subnav-horizontal-padding;
  }

  .patient-data-subnav > div:last-child {
    display: none;
  }

  .patient-data-footer-outer {
    display: none;
    a {
      display: none;
    }
  }
}
