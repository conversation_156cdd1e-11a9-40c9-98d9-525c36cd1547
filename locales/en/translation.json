{" ({{bloodGlucoseTargetSchedulesMin}}-{{bloodGlucoseTargetSchedulesMax}} {{bgUnits}})": "", " from {{count}} readings": "", " I am 12 years old or younger.": "", " I am 18 years old or older.": "", " I am between 13 and 17 years old. You'll need to have a parent or guardian agree to the terms below.": "", " in the requested format": "", " reports the data.": "", "\"{{name}}\" clinic created": "", "(days with no boluses have been excluded)": "", "(from '{{source}}')": "", "(Hyper event description)": "", "(Hypo event description)": "", "{{automatedLabel}} Exited": "", "{{clinicianName}} will lose all access to this clinic workspace and its patient list. Are you sure you want to remove this user?": "", "{{days}} days ago": "", "{{diff}} {{unit}} ago": "", "{{diff}} {{unit}} ago_plural": "{{diff}} {{unit}} ago", "{{displayName}}": "", "{{field}} cannot be in the future!": "", "{{field}} is invalid.": "", "{{field}} is not a complete date.": "", "{{field}} is required.": "", "{{field}} must be at least {{min}} characters long.": "", "{{field}} must be at most {{max}} characters long.": "", "{{field}} must not contain white spaces.": "", "{{name}} has been removed from the clinic.": "", "{{name}} has been removed.": "", "{{name}} will lose all access to this clinic workspace and patient list. Are you sure you want to remove this user?": "", "{{name}} will lose all access to your data. Are you sure you want to leave this clinic?": "", "{{name}} will lose all access to your data. Are you sure you want to remove this user from your care team?": "", "{{name}} Workspace": "", "{{numVisibleDays}} days in view": "", "{{patientName}} does not have any data yet.": "", "{{source}} data from {{count}} readings": "", "{{source}} pumps don't capture the details of how combo boluses are split between the normal and extended amounts.": "", "{{title}} (cont.)": "", "{{yearsAgo}} years ago": "", "{{yrsAgo}} years old": "", "* Animas pumps don't capture the details of how combo boluses are split between the normal and extended amounts.": "", "* Due to how carb ratios are uploaded from this pump, there may be a slight discrepancy between the value entered on the pump and the value displayed here.": "", "* This BG value was {{value}}er than your device could record. Your actual BG value is {{value}}er than it appears here.": "", "**Avg. Daily Carbs**: All carb entries added together, then divided by the number of days in this view. Note, these entries come from either bolus wizard events, or Apple Health records.": "", "**Avg. Daily Insulin:** All basal and bolus insulin delivery (in Units) added together, divided by the number of days in this view.": "", "**Avg. Glucose ({{bgSourceLabel}}):** All {{bgSourceLabel}} glucose values added together, divided by the number of readings.": "", "**CV (Coefficient of Variation):** How far apart (wide) glucose values are; research suggests a target of 36% or lower.": "", "**Daily Insulin:** All basal and bolus insulin delivery (in Units) added together.": "", "**GMI (Glucose Management Indicator):** Tells you what your approximate A1C level is likely to be, based on the average glucose level from your CGM readings.": "", "**How we calculate this:**\n\n**(%)** is the duration in {{automatedLabel}} divided by the total duration of basals for this time period.\n\n**(time)** is 24 hours multiplied by % in {{automatedLabel}}.": "", "**How we calculate this:**\n\n**(%)** is the duration in {{automatedLabel}} divided by the total duration of basals for this time period.\n\n**(time)** is total duration of time in {{automatedLabel}}.": "", "**How we calculate this:**\n\n**(%)** is the duration in {{overrideLabel}} divided by the total duration of settings overrides for this time period.\n\n**(time)** is 24 hours multiplied by % in {{overrideLabel}}.": "", "**How we calculate this:**\n\n**(%)** is the duration in {{overrideLabel}} divided by the total duration of settings overrides for this time period.\n\n**(time)** is total duration of time in {{overrideLabel}}.": "", "**How we calculate this:**\n\n**(%)** is the number of readings in range divided by all readings for this time period.\n\n**(time)** is 24 hours multiplied by % in range.": "", "**How we calculate this:**\n\n**(%)** is the number of readings in range divided by all readings for this time period.\n\n**(time)** is number of readings in range multiplied by the {{cbgLabel}} sample frequency.": "", "**How we calculate this:**\n\n**(%)** is the respective total of basal or bolus delivery divided by total insulin delivered for this time period.": "", "**Readings In Range:** Daily average of the number of {{smbgLabel}} readings.": "", "**SD (Standard Deviation):** How far values are from the average.": "", "**Sensor Usage:** Time the {{cbgLabel}} collected data, divided by the total time represented in this view.": "", "**Time In {{automatedLabel}}:** Daily average of the time spent in automated basal delivery.": "", "**Time In {{automatedLabel}}:** Time spent in automated basal delivery.": "", "**Time In {{overrideLabel}}:** Daily average of the time spent in a settings override.": "", "**Time In {{overrideLabel}}:** Time spent in a settings override.": "", "**Time In Range:** Daily average of the time spent in range, based on {{cbgLabel}} readings.": "", "**Time In Range:** Time spent in range, based on {{cbgLabel}} readings.": "", "**Total Carbs**: All carb entries from bolus wizard events or Apple Health records added together.": "", "**Total Insulin:** All basal and bolus insulin delivery (in Units) added together": "", "**Total Insulin:** All basal and bolus insulin delivery (in Units) added together, divided by the number of days in this view": "", "**Why is this stat empty?**\n\nThere is not enough data present in this view to calculate it.": "", "% CGM Use": "", "% GMI": "", "% Time in Range": "", "0-249": "", "1 U of Insulin Covers (g/U)": "", "1 U of Insulin Decreases BG by": "", "1 week": "", "1 year ago": "", "1 year old": "", "100% of Readings": "", "1000+": "", "14 days": "", "2 weeks": "", "24 hours": "", "250-499": "", "30 days": "", "4 weeks": "", "50% of Readings": "", "500-999": "", "7 days": "", "80% of Readings": "", "A basal rate segment may be missing here because it wasn't reported to the Tidepool API in sequence. We can't be 100% certain of your basal rate here.": "", "A cgm type must be specified": "", "a few seconds ago": "", "A popup blocker is preventing your report from opening.": "", "A pump type must be specified": "", "about": "", "above": "", "Above": "", "Accept": "", "Accept Invite": "", "Accept patient share invites": "", "Access Management": "", "Account": "", "Account Settings": "", "Account type is required": "", "Activate Pod": "", "Active": "", "Active Insulin Time": "", "Activity": "", "ADCES Foundation": "", "Add": "", "Add a new tag...": "", "+ Add an Additional basal rate": "", "+ Add an Additional carb ratio": "", "+ Add an Additional correction range": "", "+ Add an Additional insulin sensitivity factor": "", "Add and remove clinician users": "", "Add another": "", "ADD EMAIL": "", "Add New Patient": "", "Add New Patient Account": "", "Add New Prescription": "", "Add Patient": "", "Add your birthday, diagnosis date, and type": "", "Alert: Starting 9/15/2020 the minimum supported version of the Uploader will be V2.31.0.": "", "All calculations must be confirmed by the provider before use. The suggested results are not a substitute for clinical judgment.": "", "All changes saved.": "", "All Data": "", "Allow upload of data": "", "Allow upload permission": "", "Allow uploading": "", "Allows for guided entry of total daily dose, weight, or both to generate basal rates, carbohydrate to insulin ratio, and insulin sensitivity factor.": "", "An error occurred while logging in.": "", "An insulin model must be specified": "", "An unknown error occurred": "", "and below": "", "Anonymous user": "", "Anything you would like to share?": "", "Apply": "", "Apply Filter": "", "Approx {{numVisibleDays}} days in view": "", "April": "", "Are you sure you want to cancel your invitation to {{email}}?": "", "Are you sure you want to decline this share invite from {{patient}}?": "", "Are you sure you want to leave this person's Care Team? You will no longer be able to view their data.": "", "Are you sure you want to remove this person? They will no longer be able to see or comment on your data.": "", "Are you sure you want to start {{patientName}} with the below therapy settings order?": "", "Assign Patient Tags": "", "Assigned Patient Tags": "", "At least three days do not have boluses, so this statistic might not be right": "", "August": "", "Auto": "", "Auto Mode": "", "automated": "", "Automated": "", "Automated Suspend": "", "Automation": "", "Available Patient Tags": "", "Available Tags": "", "Average BG": "", "Avg BG readings / day": "", "Avg boluses / day": "", "Avg per day": "", "Avg. BG ({{- units}})": "", "Avg. BG Readings / Day": "", "Avg. Daily Carb Exchanges": "", "Avg. Daily Carbs": "", "Avg. Daily Insulin": "", "Avg. Daily Insulin Ratio": "", "Avg. Daily Readings In Range": "", "Avg. Daily Time In {{automatedLabel}}": "", "Avg. Daily Time In {{overrideLabel}}": "", "Avg. Daily Time In Range": "", "Avg. Daily Total Insulin": "", "Avg. Glucose ({{bgSourceLabel}})": "", "Back": "", "Back to Patient List": "", "Back To Prescriptions": "", "Basal": "", "Basal Events": "", "Basal Insulin": "", "Basal insulin keeps your glucose within your target range when you are not eating.  Bolus insulin is mostly used to cover the carbohydrates you eat or to bring a high glucose back into your target range. This ratio allows you to compare how much of the insulin you are taking is used for each purpose. We will only show numbers if there is enough basal data - 24 hours in the one day view and 7 days in the two week view.": "", "Basal rate is required": "", "Basal Rate: ": "", "Basal Rates": "", "Basal Rate Values (U/hr)": "", "Basal:Bolus Ratio": "", "Basals": "", "Basics": "", "Basics Chart": "", "Because {{source}} represents extended boluses that cross midnight as two boluses, this bolus could be part of a dual-wave bolus, not an independent square-wave bolus as represented here.": "", "Because of how {{source}} reports bolus and wizard data, we can't be 100% certain that the bolus wizard information here (e.g., carbs, suggested dose) corresponds with the bolus.": "", "Because of how {{source}} reports bolus data, normal and square-wave boluses may not be properly combined to appear as a dual-wave bolus.": "", "Because of how {{source}} reports the data, we could not determine the duration of this basal rate segment.": "", "Because there was a time change during this basal, we split the basal into two segments.": "", "Before accessing your clinic workspace, please provide the additional account information requested below.": "", "below": "", "Below {{threshold}}": "", "between": "", "Beyond Type 1": "", "BG": "", "BG Extents ({{bgSourceLabel}})": "", "BG Log": "", "BG Log Chart": "", "BG readings": "", "BG Target": "", "BGM": "", "Birthdate": "", "Birthdate not known": "", "birthday": "", "Birthday": "", "Birthday is invalid.": "", "Birthday is required.": "", "Blood Glucose": "", "Bolus": "", "Bolus Calculator": "", "Bolus Insulin": "", "Bolus Wizard": "", "Bolusing": "", "Born this year": "", "By creating this clinic, your Tidepool account will become the default administrator. You can invite other healthcare professionals to join the clinic and add or remove privileges for these accounts at any time.": "", "Calculate": "", "Calculator": "", "Calibrations": "", "Cancel": "", "Cancel Update": "", "Cannot create new chart with no data": "", "Cannula Fills": "", "Carb exch": "", "carb exchanges": "", "Carb Ratios": "", "Carbohydrate Ratio": "", "Carbohydrate Ratio: ": "", "Carbohydrates": "", "carbs": "", "Carbs": "", "Carbs (g)": "", "CGM": "", "CGM data will be synced from Dexcom": "", "CGM Use <{{minCgmPercent}}%": "", "CGM Use <{{minCgmHours}} hours": "", "Change": "", "Change Cartridge": "", "Change clinician permissions": "", "Change Pod": "", "Change your password": "", "Chart Date": "", "Chart Date Range": "", "Check your email and follow the instructions to reset your password.": "", "Check your email and follow the link there. (We need to confirm that you are really you.)": "", "Children with Diabetes": "", "Choose a diabetes nonprofit": "", "Choose One": "", "Choose which diabetes organization(s) to support": "", "Choose workspace": "", "City": "", "Claimed": "", "Clear": "", "Click a tag's text to rename it, or click the trash can icon to delete it.": "", "Click here to complete the \"Set up data storage\" step.": "", "Clinic Address": "", "Clinic Admin": "", "Clinic Admin can:": "", "Clinic admins have complete access to a workspace and can manage patients, clinicians and the clinic profile.": "", "Clinic Manager": "", "Clinic Member": "", "Clinic Member can:": "", "Clinic Members": "", "Clinic members have limited access to a workspace and can only manage patients.": "", "Clinic migration in progress. You will be automatically logged out.": "", "Clinic Name": "", "Clinic phone number is required": "", "Clinic Profile": "", "Clinic profile updated.": "", "Clinic Share Code": "", "Clinic Workspace": "", "Clinician Account": "", "Clinician invite resent to {{email}}.": "", "Clinician invite sent.": "", "Clinician invite to {{email}} has been revoked.": "", "Clinician removed from clinic.": "", "Clinician Roles and Permissions": "", "Clinician Table": "", "Close": "", "The Diabetes Link": "", "Combo /": "", "Comment_submit": "Comment", "Complete Patient Profile": "", "Complete your profile.": "", "Confirm": "", "Confirm BG": "", "Confirm Clinic Migration": "", "Confirm Email Address": "", "Confirm new password": "", "confirm password": "", "Confirm password": "", "Confirm Resending Invite": "", "Confirm Revoking Invite": "", "Confirm Therapy Settings": "", "Connect with Dexcom": "", "Continue": "", "Control-IQ": "", "Copied ✓": "", "Copied!": "", "Copy as text": "", "Copy Share Code": "", "Copy therapy settings order as text": "", "Copy this page\\’s URL": "", "Copy to clipboard": "", "Correct Above": "", "Correction": "", "Correction factor": "", "Correction Range": "", "Country": "", "Country code is required": "", "CPT Training Required": "", "Create a New Clinic": "", "Create and manage access to patient accounts without requiring Tidepool Uploader.": "", "Create Clinician Account": "", "Create New Prescription": "", "Create Password": "", "Create Patient Account": "", "Create patient accounts": "", "Create Personal Account": "", "Create Tidepool Account": "", "Creating Clinician Account...": "", "Creating Personal Account...": "", "CV ({{bgSourceLabel}})": "", "Daily": "", "Daily Charts": "", "Date of birth": "", "Date of Birth:": "", "Date of diagnosis": "", "Date range: {{dateRange}}": "", "day": "", "Day": "", "day_timeago": "day", "days": "", "Days In Report": "", "Days with no boluses have been excluded from bolus calculations": "", "days_timeago": "days", "ddd, MMM D, YYYY": "", "December": "", "Decline": "", "Decline {{name}}": "", "Decline Invite": "", "Decline invite?": "", "Delete my account": "", "Delete prescription": "", "delivered": "", "Delivered": "", "Delivery Limits": "", "Derived from _**{{total}}**_ {{smbgLabel}} readings.": "", "Derived from _**{{total}}**_ carb entries.": "", "Deselect All": "", "Device settings": "", "Device Settings": "", "Dexcom API": "", "Diabetes Educator": "", "Diabetes Youth Families (DYF)": "", "DiabetesSisters": "", "Diagnosed {{diagnosisDate}}": "", "Diagnosed {{diagnosisDate}} as {{diagnosisType}}": "", "Diagnosed as": "", "Diagnosed as {{diagnosisType}}": "", "diagnosis date": "", "Diagnosis date": "", "Diagnosis date not known": "", "Directly manage Clinic team member access.": "", "Discard Changes": "", "Discard Invite": "", "Disconnect": "", "Do you want us to resend the email? Enter the address you used to signup below.": "", "DOB:": "", "DOB: {{birthdate}}": "", "Does {{patientName}} have the necessary prescriptions for Tidepool Loop compatible devices?": "", "Donate my anonymized data": "", "Donate my data?": "", "Donate their anonymized data": "", "Donate your data. Contribute to research.": "", "Donating anonymized data...": "", "Done": "", "Download Latest": "", "Draft": "", "Duration of Insulin Action": "", "Edit": "", "Edit {{label}}": "", "Edit Available Patient Tags": "", "Edit clinic details": "", "Edit Clinic Profile": "", "Edit Clinician Information": "", "Edit Patient Details": "", "Edit Patient Information": "", "Edit Patient Name": "", "Edit tags": "", "Edit therapy settings": "", "Email": "", "Email (optional)": "", "email address": "", "Email Address": "", "Email address confirmation does not match": "", "Email address is invalid": "", "Email address is required": "", "Email confirmation is required": "", "Email sent!": "", "email to invite them to upload and view data from home": "", "Email:": "", "Endocrinologist": "", "Enter email address": "", "Enter Patient's Total Daily Dose": "", "Enter Patient's Weight": "", "Enter share code": "", "Enter the 12 digit Clinic share code provided to you": "", "Enter the email address of the new care team member": "", "Enter Therapy Settings": "", "Exclude days with no boluses": "", "Exercise": "", "Expired": "", "Export": "", "Export My Data": "", "Export my data from:": "", "Exported from Tidepool": "", "Exported from Tidepool: {{today}}": "", "Extended": "", "ezCarb ezBG": "", "F_Friday": "F", "February": "", "Female": "", "File type:": "", "Fill Cannula": "", "Fill Tubing": "", "Filter": "", "Filter By": "", "Filter by Patient Tags": "", "First": "", "First Name": "", "First name is required": "", "For email or notes": "", "For first time users of an automated system, Tidepool suggests you start with 3x your highest basal rate.": "", "Forgot your password?": "", "from": "", "Front Desk": "", "full name": "", "Full name": "", "Full Name": "", "Full name is required": "", "g/U": "", "Gender": "", "Gestational": "", "Get Started": "", "Get Support": "", "Get the Tidepool Uploader": "", "Glucose Safety Limit": "", "Glucose Settings": "", "Glycemic Events": "", "GMI ({{bgSourceLabel}})": "", "Go Prime": "", "Go Rewind": "", "Go To Workspace": "", "Greater than": "", "Group": "", "Hang on there, skippy! You unselected all of the data!": "", "has not verified their email": "", "Health Professions Student": "", "Healthcare System": "", "Hey, you're not verified yet.": "", "high": "", "High": "", "High target is required": "", "Hmm, this date doesn’t look right": "", "hour": "", "hour_timeago": "hour", "hours": "", "hours_timeago": "hours", "How do you describe their diabetes?": "", "How do you describe your diabetes?": "", "How many patients does your clinic practice see?": "", "html.basics-no-uploaded-data": "<0>The Basics view shows a summary of your recent device activity, but it looks like you haven't uploaded device data yet.</0><1>To see the Basics, <1>upload</1> some device data.</1><2>If you just uploaded, try <1>refreshing</1>.</2>", "html.bg-log-no-uploaded-data": "<0>The BG Log view shows a history of your finger stick BG data, but it looks like you haven't uploaded finger stick data yet.</0><1>To see your data in the BG Log view, <1>upload</1> your pump or BG meter.</1><2>If you just uploaded, try <1>refreshing</1>.</2>", "html.browser-warning-text": "Paste the copied URL into <1>Chrome or Edge</1>... ", "html.dexcom-datasource-intl-disclaimer": "<0>*For US Dexcom users only. Please contact <1><EMAIL></1> if you live outside the United States.</0>", "html.donate-form-explainer": " You own your data. Read all the details about Tidepool's Big Data Donation project <1>here</1>.", "html.emailverification-instructions": "<0>Keeping your data private and secure is important to us!</0><1><0>Please click the link in the email we just sent you at<1></1><2><0>{{sent}}</0></2><3></3>to verify and activate your account.</0></1>", "html.export-error": "An error occurred attempting to export your data. This may be temporary and you can try the export again. If the error continues, please contact support.", "html.patient-delete-account": "If you are sure you want to delete your account, <1>send an email</1> to <EMAIL> and we take care of it for you.", "html.patient-info-fullname": "<0>{{fullName}}</0> (edit in <2>account</2>)", "html.patientdata-uploaded-message": "<0><0>To upload your data, install Tidepool Uploader</0><1></1><2>If you already have Tidepool Uploader, launch it <1>here</1></2><3><0>Sync CGM Data</0><1>Connect With</1></3><4>To upload <PERSON><PERSON> with <PERSON>, get <1>Tidepool Mobile</1></4></0><1>Already uploaded? <1>Click to reload.</1><2></2><3>Need help?</3> Email us at <5><EMAIL></5> or visit our <7>help page</7>.</1>", "html.patientinfo-units-used": "<0>The units I use are</0><1><0></0></1>", "html.patientnew-donate-explainer": " You own your data. Read all the details about Tidepool's Big Data Donation project <1>here</1>.", "html.patients-no-data": "Looks like you don’t have access to any data yet.<1></1>Please ask someone to invite you to see their data.", "html.patients-setup-data-storage": "You can also <1>setup data storage</1> for someone’s diabetes data.", "html.patientsettings-target-range": "My target range <1>is</1>", "html.peopletable-instructions": "Type a patient name in the search box or click <1>Show All</1> to display all patients.", "html.peopletable-remove-patient-confirm": "<0><0>Are you sure you want to remove patient: </0><1>{{fullName}}</1><2> from your list?</2></0><1>You will no longer be able to see or comment on their data.</1>", "html.peopletable-remove-patient-tag-confirm": "<0>Are you sure you want to remove the tag: <1><0>{{name}}</0></1> from the clinic?</0><1>This tag will also be removed from any patients who have been tagged with it.</1>", "html.setting-no-uploaded-data": "", "html.signup-clinician": "If you are a Healthcare Provider and want to create an account, please <1>click here</1>.", "html.signup-invited": "<0>You've been invited to Tidepool.</0><1>Sign up to view the invitation.</1>", "html.signup-personal": " If you are a provider who lives with diabetes and wants to track and manage your personal diabetes data, please create a separate <1>personal account</1>.", "html.signup-terms-of-use": "I accept the terms of the Tidepool Applications <1>Terms of Use</1> and <3>Privacy Policy</3>", "html.summary-stat-info": "<0><0>Warning:</0> % CGM Use, GMI, and % Time in Range may not match the patient profile if older data is added after the summary statistics have already been calculated.</0>", "html.terms-accept-of-age": "I am 18 or older and I accept the terms of the <1>Tidepool Applications Terms of Use</1> and <3>Privacy Policy</3>", "html.terms-accept-on-behalf": "I agree that my child aged 13 through 17 can use Tidepool Applications and agree that they are also bound to the terms of the <1>Tidepool Applications Terms of Use</1> and <3>Privacy Policy</3>", "html.terms-of-use-updated": "The Terms of Use and Privacy Policy have changed since you last used Tidepool.<1></1>You need to accept the changes to continue.", "html.uploadlaunchoverlay-error": "Error fetching release information, please go to our<1> downloads page</1>.", "html.uploadlaunchoverlay-launching": "<0>Launching Uploader</0><1>If you don't yet have the Tidepool Uploader, please install the appropriate version below</1>", "Hyperglycemia": "", "Hypoglycemia": "", "I have confirmed the therapy settings order for this patient": "", "I:C Ratio": "", "I'm sure, remove me.": "", "I'm sure, remove them": "", "IC ratio": "", "If the Clinic Name is incorrect, please go back and check the 12 digit share code you entered.": "", "If the data here overlaps, it's because the date/time was changed and {{source}} pumps don't capture when this happened.": "", "If this option is checked, days without boluses will be excluded when calculating this stat and the \"Avg per day\" count in the \"Bolusing\" calendar summary.": "", "If this option is checked, the target ranges for this view will be updated to the default ranges.": "", "If you are unsure, Tidepool’s recommendation is to start with 1700 / TDD.": "", "If you decline this invite you will need to ask your Clinic Admin to send a new one. Are you sure you want to decline the invite to the {{name}} clinic workspace? ": "", "If you don’t wear a CGM or don’t have enough CGM data, to get one number that gives you a rough idea of your glucose level, we add together all of the fingerstick readings you have and then divide them by the number of readings. We will only show a number if there is enough data - at least 4 readings in the one day view, and at least 4 readings for at least half of the days shown in the two week view.": "", "If you want your patients to upload their data from home, you must include their email address.": "", "Ignore": "", "Inactive": "", "Infusion site changes": "", "Infusion site changes are not yet available for all pumps. Coming soon!": "", "Initial pump settings order": "", "Insulin Duration": "", "Insulin Model": "", "Insulin Sensitivity": "", "Insulin Sensitivity Factor": "", "Insulin Sensitivity Factors": "", "Insulin Sensitivity: ": "", "Insulin Settings": "", "Insulin to Carb Ratios": "", "Insulin to Carbohydrate Ratios": "", "Insulin-to-carb ratio is required": "", "Interrupted": "", "Invalid email address.": "", "Invite": "", "invite declined": "", "Invite Member": "", "Invite New Clinic Team Member": "", "Invite new member": "", "invite sent": "", "Invite Team Members": "", "Invite to {{name}} has been declined.": "", "Invites ({{count}})": "", "IOB": "", "ISF": "", "IT/Technology": "", "January": "", "JDRF": "", "Job Title": "", "Join the team!": "", "Joining {{name}}'s team...": "", "July": "", "June": "", "kg": "", "LADA (Type 1.5)": "", "Language": "", "Last": "", "Last 14 days": "", "Last 14 Days": "", "Last 2 days": "", "Last 30 days": "", "Last 30 Days": "", "Last 90 Days": "", "Last data {{timeAgo}}": "", "Last Name": "", "Last name is required": "", "Last updated {{timeAgo}} {{timeAgoUnits}} ago": "", "Last Upload": "", "Last Upload (CGM)": "", "lbs": "", "Learn More": "", "Leave {{name}}": "", "Leave Clinic": "", "Legend": "", "Less than": "", "less than an": "", "Leverage a Clinic Share Code to connect with patients.": "", "Lines": "", "Log in": "", "Log in to view the invitation.": "", "Logging in...": "", "Logging out...": "", "Login": "", "Login expired - try signing out & in again": "", "Logout": "", "Looks like you've already sent an invitation to that email.": "", "Looks like your e-mail address has not been verified.": "", "low": "", "Low": "", "Low target is required": "", "Lower": "", "Lower Target": "", "Lower target must be less than upper target.": "", "Lower Target: {{lowWarning}}": "", "M_Monday": "M", "M-D-YYYY": "", "Mac or Windows.": "", "Made possible by": "", "Male": "", "Manage Workspaces": "", "Manual": "", "manual & automated": "", "March": "", "Max Basal": "", "Max Basal Rate": "", "Max basal rate is required": "", "Max BG": "", "Max Bolus": "", "Max bolus amount is required": "", "Maximum basal rate is the automatically adjusted basal rate that Tidepool Loop is allowed to enact to help reach your correction range.": "", "Maximum Bolus": "", "Maximum bolus is the highest bolus amount that you will allow Tidepool Loop to recommend at one time to cover carbs or bring down high glucose.": "", "May": "", "Median": "", "Medical Assistant": "", "Medical Record Number": "", "Meter": "", "mg/dL": "", "Min BG": "", "minute": "", "minute_timeago": "minute", "minutes": "", "minutes_timeago": "minutes", "MM/DD/YYYY": "", "MMM D, YYYY": "", "mmol/L": "", "Mobile Number": "", "MODY/Monogenic": "", "month": "", "Month": "", "month_timeago": "month", "months": "", "months_timeago": "months", "More about your clinic": "", "MRN": "", "MRN (optional)": "", "MRN: {{mrn}}": "", "My Data Sources": "", "Name": "", "Name is required": "", "New password": "", "New Prescription Form": "", "New Tidepool Account? Share Your Data with your healthcare team.": "", "Next": "", "Nightscout Foundation": "", "No": "", "No available data to display here, sorry!": "", "No data available": "", "No data available - click Connect to enable": "", "No data found": "", "No, not now": "", "No, Patient can self start with Tidepool Loop in-app tutorial": "", "Normal": "", "Not required": "", "Not set": "", "Not specified": "", "November": "", "Number of days (most recent)": "", "Nurse/Nurse Practitioner": "", "October": "", "Only some of the days within the current range contain bolus data.": "", "Only standard characters are allowed": "", "Open it anyway": "", "Open navigation menu": "", "Optional Therapy Settings Calculator": "", "Or select a custom date range ({{maxDays}} days max)": "", "Other": "", "over {{hoursAgo}}": "", "over 24": "", "Override": "", "Override up & down": "", "Page {{page}} of {{pageCount}}": "", "password": "", "Password": "", "Password is invalid.": "", "Password is required.": "", "Password must be at least {{minLength}} characters long.": "", "Passwords don't match.": "", "Patient": "", "Patient and caregiver": "", "Patient Details": "", "Patient Email (optional)": "", "Patient gender is required": "", "Patient invite for {{name}} has been accepted.": "", "Patient invite for {{name}} has been declined.": "", "Patient List": "", "Patient MRN number is required": "", "Patient phone number is required": "", "Patient Profile": "", "Patient Tags": "", "Patient's birthday is required": "", "Patients": "", "Pending Approval": "", "Permission": "", "Personal Account": "", "Pharmacist": "", "Phone Number": "", "Physician Assistant": "", "Please choose a preferred site change source from the 'Basics' web view to view this data.": "", "Please confirm the therapy settings for this patient": "", "Please enable at least one chart to print": "", "Please enter a {{field}} that comes after the birthday.": "", "Please enter a city": "", "Please enter a country": "", "Please enter a date prior to today": "", "Please enter a date within the last 130 years": "", "Please enter a diagnosis date that comes after the birthday": "", "Please enter a share code": "", "Please enter a state": "", "Please enter a valid email address": "", "Please enter a valid phone number": "", "Please enter a valid share code": "", "Please enter a valid state": "", "Please enter a valid website address": "", "Please enter a valid website address with https:// at the beginning": "", "Please enter a valid zip/postal code": "", "Please enter a zip/postal code": "", "Please enter an address": "", "Please enter an organization name": "", "Please enter dates using MM/DD/YYYY format": "", "Please enter the patient's full name": "", "Please enter the patient's name and birthdate": "", "Please enter your email address.": "", "Please keep \"about\" text under {{maxLength}} characters": "", "Please press Ctrl + C now": "", "Please select a clinic type": "", "Please select a date range": "", "Please select a valid option": "", "Please select a value between {{min}}-{{max}}": "", "Please select an organization size": "", "Please select your preferred BG units": "", "Please set a valid blood glucose units option": "", "Please set a valid country code": "", "Please set a valid prescription status": "", "Please wait while Tidepool generates your PDF report.": "", "Post_submit": "Post", "Pre-diabetes": "", "Pre-meal Correction Range": "", "Prefer not to specify": "", "Preferred blood glucose units": "", "Preparing Chart Data": "", "Prescriber": "", "Prescribing access": "", "Prescription: {{name}}": "", "Prescriptions": "", "Previous": "", "Previous Step": "", "Primary Care Physician": "", "Prime": "", "Prime Cannula": "", "Prime Reservoir": "", "Print": "", "Print Report": "", "Privacy and Terms of Use": "", "Private Workspace": "", "Profile": "", "Profile Settings": "", "Profile updated": "", "Programmed": "", "Provide every member of your team with their own unique account and login credentials to access patient data.": "", "Provider Practice": "", "Pump Settings": "", "Questions or feedback? <NAME_EMAIL> or visit support.tidepool.org.": "", "Range": "", "Range & Average": "", "Rapid Acting - Adult": "", "Rapid Acting - Child": "", "Rapid-Acting - Adult Model": "", "Rapid-Acting - Child Model": "", "Readings Above Range": "", "Readings Below Range": "", "Readings In Range": "", "Recommended default settings from AACE calculator:": "", "Refresh": "", "Refresh patients list": "", "Remember me": "", "Remove": "", "Remove \"{{name}}\"": "", "Remove {{clinicianName}}": "", "Remove {{memberType}}": "", "Remove {{name}}": "", "Remove Patient": "", "Remove patients from patient list": "", "Remove People": "", "Remove upload permission": "", "Remove User": "", "Request for certified pump trainer (CPT) in-person training. Required (TBD) for patients new to {{displayName}}.": "", "Required": "", "Research Organization": "", "Resend": "", "Resend Invite": "", "Reservoir Changes": "", "Reset Filters": "", "Reset to default": "", "Return to login": "", "Review and Save Prescription": "", "Revoke Invite": "", "Revoke Invite?": "", "Rewind": "", "Role": "", "Sa_Saturday": "Sa", "Save": "", "Save and Continue": "", "Save changes": "", "Save Changes": "", "Save Prescription": "", "Save Profile": "", "Saved": "", "Saving...": "", "scheduled": "", "Search": "", "Search by Name": "", "Search Entries": "", "See all your diabetes data in one place. Finally.": "", "See all your patients and all their device data in one place.": "", "See the Update Guide": "", "Select a specific day": "", "Select All": "", "Select language...": "", "Select method": "", "Select one": "", "Selected": "", "Selected Tags": "", "Send ": "", "Send Invite": "", "Send reset link": "", "Send Upload Reminder": "", "Sending email...": "", "Sending...": "", "Sensitivity": "", "Sensitivity factor is required": "", "Sensor Usage": "", "September": "", "Serial Number": "", "Serial Number: {{serial}}": "", "Set up data storage": "", "Setting up...": "", "Settings Override": "", "Severe hyperglycemia": "", "Severe hypoglycemia": "", "Share": "", "Share a bit about this person.": "", "Share a bit about yourself.": "", "Share access for {{name}} has been revoked.": "", "Share data": "", "Share Data": "", "Share invite to {{clinic}} has been sent.": "", "Share invite to {{email}} has been resent.": "", "Share invite to {{email}} has been revoked.": "", "Share invite to {{email}} has been sent.": "", "Share invite to {{name}} has been revoked.": "", "Share with a Care Team Member": "", "Share with a Clinic": "", "Share your data": "", "sharing": "", "Showing {{source}} data": "", "Sign up": "", "Sign Up for Tidepool": "", "Skip": "", "Sleep": "", "Some entries are invalid.": "", "Something went wrong resending an outgoing invitation to a care team.": "", "Something went wrong sending an outgoing invitation to a care team.": "", "Something went wrong sending an outgoing invitation to a clinic.": "", "Something went wrong trying to leave a care team.": "", "Something went wrong trying to remove a member from a care team.": "", "Something went wrong trying to request a password reset e-mail.": "", "Something went wrong trying to resend verification e-mail.": "", "Something went wrong trying to sign you up.": "", "Something went wrong while accepting a received care team invitation.": "", "Something went wrong while accepting clinician invite.": "", "Something went wrong while accepting patient invitation.": "", "Something went wrong while accepting the terms and conditions.": "", "Something went wrong while cancelling an outgoing care team invitation.": "", "Something went wrong while changing care team member permissions.": "", "Something went wrong while closing the connection to your data.": "", "Something went wrong while confirming your sign-up.": "", "Something went wrong while connecting the data source.": "", "Something went wrong while creating a message thread.": "", "Something went wrong while creating clinic.": "", "Something went wrong while creating patient account.": "", "Something went wrong while creating the patient tag.": "", "Something went wrong while creating your prescription.": "", "Something went wrong while deleting clinician from clinic.": "", "Something went wrong while deleting clinician invite.": "", "Something went wrong while deleting patient from clinic.": "", "Something went wrong while deleting patient invitation.": "", "Something went wrong while deleting the patient tag.": "", "Something went wrong while deleting your prescription.": "", "Something went wrong while disconnecting the data source.": "", "Something went wrong while dismissing clinician invite.": "", "Something went wrong while editing a message thread.": "", "Something went wrong while fetching a message thread.": "", "Something went wrong while fetching associated accounts.": "", "Something went wrong while fetching clinic.": "", "Something went wrong while fetching clinician invite.": "", "Something went wrong while fetching clinician invites.": "", "Something went wrong while fetching clinician.": "", "Something went wrong while fetching clinicians from clinic.": "", "Something went wrong while fetching clinics for patient.": "", "Something went wrong while fetching data for the current patient.": "", "Something went wrong while fetching data for the current patient. You are not authorized to view this patient data.": "", "Something went wrong while fetching latest pump settings for the current patient.": "", "Something went wrong while fetching latest pump settings upload record for the current patient.": "", "Something went wrong while fetching one or more clinics.": "", "Something went wrong while fetching patient from clinic.": "", "Something went wrong while fetching patient invites.": "", "Something went wrong while fetching patient preferences.": "", "Something went wrong while fetching patient settings.": "", "Something went wrong while fetching patient.": "", "Something went wrong while fetching patient. You are not authorized to view this patient.": "", "Something went wrong while fetching patients for clinic.": "", "Something went wrong while fetching pending outgoing care team invitations.": "", "Something went wrong while fetching received invitations to others' care teams.": "", "Something went wrong while fetching the devices list.": "", "Something went wrong while fetching the server time.  Falling back to local machine time": "", "Something went wrong while fetching user.": "", "Something went wrong while fetching your data sources.": "", "Something went wrong while fetching your prescriptions.": "", "Something went wrong while generating your report.": "", "Something went wrong while getting clinics for clinician.": "", "Something went wrong while getting clinics.": "", "Something went wrong while migrating this clinic.": "", "Something went wrong while processing your data.": "", "Something went wrong while querying your data.": "", "Something went wrong while rejecting a received care team invitation.": "", "Something went wrong while resending clinician invite.": "", "Something went wrong while saving patient BG unit settings.": "", "Something went wrong while saving patient preferences.": "", "Something went wrong while saving patient profile.": "", "Something went wrong while saving patient settings.": "", "Something went wrong while sending an upload reminder to the patient.": "", "Something went wrong while sending clinician invite.": "", "Something went wrong while setting up data storage.": "", "Something went wrong while updating clinic patient.": "", "Something went wrong while updating clinic.": "", "Something went wrong while updating clinician.": "", "Something went wrong while updating patient permissions.": "", "Something went wrong while updating the patient tag.": "", "Something went wrong while updating user account.": "", "Something went wrong while updating your data donation preferences.": "", "Something went wrong while updating your data.": "", "Something went wrong while updating your prescription.": "", "Something went wrong with your account authorization. Maybe try logging out and then logging back in? If you're still having issues then please contact support.": "", "Something went wrong with your account authorization. Please check with your administrator to verify your level of access.": "", "Sorry but it appears that you are offline. Tidepool requires that you be connected to the internet.": "", "Sorry, there was nothing to copy.": "", "Sorry, you already have a tag with that name.": "", "Sorry, you already have the maximum number of patient tags.": "", "Sorry! It appears that this account hasn't been fully set up. Please notify the account owner that data storage may not be set up for this account.": "", "Sorry! It appears that your account hasn't been fully set up.": "", "Sorry! Something went wrong. It's our fault, not yours. We're going to go investigate. Please try again in a few moments.": "", "Sorry! Something went wrong. It's our fault, not yours. We're going to investigate.": "", "Spring forward! You may see your data overlap. Make sure you update the time on all your devices.": "", "Start by creating a new clinic.": "", "Start time": "", "Start Time": "", "Start time is required": "", "State/Province": "", "Status": "", "Status{{count}}": "", "Std. Deviation ({{bgSourceLabel}})": "", "Su_Sunday": "Su", "Submit": "", "Submit Code": "", "Submitted": "", "Success!": "", "Suggested": "", "Summary stat info": "", "Suspend threshold is required": "", "Suspends": "", "Switch between multiple Clinic Workspaces, as needed.": "", "T1D Exchange": "", "Tag created.": "", "Tag name max length is ${max} characters": "", "Tag removed.": "", "Tag updated.": "", "Target": "", "Target BG": "", "Temp basal of": "", "Temp Basals": "", "Th_Thursday": "Th", "Thanks for contributing! Donate proceeds to a diabetes nonprofit.": "", "That e-mail address already has an account.": "", "The Basics": "", "The birthday specified does not match what is in our system. Please contact the clinic that created your account and ask them to update your birthday.": "", "The correction range is the glucose range that you would like the app to correct your glucose to by adjusting insulin dosing.": "", "The date and/or time settings of your device were changed recently, and this datum may overlap in the timeline display with other data from the same device.": "", "The diaTribe Foundation": "", "The following therapy settings order has been submitted for {{patientName}}": "", "The information below will be displayed along with your name when you invite patients to connect and share their data remotely. Please ensure you have the correct clinic information for their verification.": "", "the patient": "", "The patient's phone number may be used to provide direct assistance regarding their Tidepool account. Standard messaging rates may apply.": "", "The pre-meal correction range is the glucose range that you would like the app to correct your glucose to by adjusting insulin dosing when activated up to one hour before eating so that you begin a meal in a lower target range.": "", "The value you have chosen is higher than Tidepool generally recommends.": "", "The value you have chosen is lower than Tidepool generally recommends.": "", "The workout correction range is the glucose range that you would like the app to correct your glucose to by adjusting insulin dosing when activated before, during, or after physical activity to reduce the risk of low glucose events.": "", "Therapy Settings": "", "Therapy Settings Calculator": "", "There are no invites. Refresh to check for pending invites.": "", "There are no results to show.": "", "There are unsaved changes to this clinician invite which will be lost if you navigate away. Are you sure you want to discard this invite?": "", "There are unsaved changes to this clinician’s permissions which will be lost if you navigate away. Are you sure you want to discard these changes?": "", "There is no {{displayType}} data for this time period :(": "", "There is not enough data to show this statistic.": "", "These people can view {{patientName}}'s data": "", "This basal rate was running for longer than 5 days, which we cannot display.": "", "This basal was extended by one second because the duration and time reported by {{source}} do not match up exactly.": "", "This BG value was above or below {{source}}'s threshold for reporting a numerical value. Your actual BG value was higher or lower than displayed.": "", "This clinician is already a member of the clinic.": "", "This email will be used for an account set up invitation to the end user and for all Tidepool correspondence.": "", "this field": "", "This field is required.": "", "This is for me, I have diabetes": "", "This is for someone I care for who has diabetes": "", "This model assumes peak insulin activity at {{minutes}} minutes.": "", "This patient has set a custom BG target range.": "", "This section requires data from a blood-glucose meter, so there's nothing to display.": "", "This section requires data from an insulin pump, so there's nothing to display.": "", "This suspend happened because of one of the following alarms - no power, occlusion, auto-off, or no insulin.": "", "this year": "", "Tidepool generally recommends a pre-meal range lower than your normal correction range{{bloodGlucoseTargetSchedulesExtentsText}}.": "", "Tidepool generally recommends a workout range higher than your normal correction range{{bloodGlucoseTargetSchedulesExtentsText}}.": "", "Tidepool is stuck and isn't doing what you want it to do. We're sorry for the trouble.": "", "Tidepool is unable to complete your sign-up as this verification link has expired. Please check your email for an updated link and try again.": "", "Tidepool Loop assumes that the insulin it has delivered is actively working to lower your glucose for 6 hours. This setting cannot be changed.": "", "Tidepool Loop Order Form and Treatment Plan": "", "Tidepool Loop therapy settings order": "", "Tidepool provides free, secure data storage for diabetes data.": "", "Tidepool recommends that your maximum basal rate does not exceed 6.4 times your highest scheduled basal rate of {{value}} U/hr.": "", "Tidepool recommends that your maximum basal rate is at least 2.1 times your highest scheduled basal rate of {{value}} U/hr.": "", "Tidepool Web works with Chrome or Edge on ": "", "Tidepool will attempt to send the details to our server.": "", "Tidepool will share 10% of the proceeds with the diabetes organization(s) of your choice.": "", "Time": "", "Time Above Range": "", "Time Below Range": "", "Time in {{automatedLabel}}": "", "Time In {{automatedLabel}}": "", "Time In {{automatedLabel}} Ratio": "", "Time In {{overrideLabel}}": "", "Time In {{scheduledLabel}}": "", "Time In Range": "", "Time in Target": "", "Timed Out": "", "to": "", "To get one number that gives you a rough idea of your glucose level we add together all of the CGM glucose readings you have and then divide them by the number of glucose readings. We will only show a number if there is enough data - readings for at least 75% of the day in the one day view, and readings for at least 75% of the day for at least half of the days shown in the two week view.": "", "Today": "", "Toggle visibility": "", "Total basal events": "", "Total BG Readings": "", "Total Carbs": "", "Total Daily Dose": "", "Total Daily Dose (U)": "", "Total Daily Dose and Weight": "", "Total Daily Dose is required": "", "Total Daily Dose:": "", "Total Insulin": "", "Training type is required": "", "Transfer pump settings": "", "Trends": "", "Tu_Tuesday": "Tu", "Tube Primes": "", "Type 1": "", "Type 2": "", "Type a comment here ...": "", "Type a new note here ...": "", "Type of Account": "", "U/hr": "", "undelivered": "", "Underride": "", "Units in {{bgUnits}}": "", "Units:": "", "unknown": "", "Unknown": "", "Unsaved changes": "", "Unsaved Invite": "", "Unselect all": "", "up & down": "", "Up Front ({{normalPercentage}})": "", "Update": "", "Update \"{{name}}\"": "", "Update and Review": "", "Update Member": "", "Update My Profile": "", "Update prescription": "", "Update your account": "", "Upload": "", "Upload data": "", "Upload Permission": "", "Upload permission for {{name}} has been {{uploadPermission}}.": "", "Uploaded on": "", "Uploaded on: {{uploadDate}}": "", "Uploader reminder email for {{name}} has been sent.": "", "Upper": "", "Upper Target": "", "Upper target must be greater than lower target.": "", "Upper Target: {{highWarning}}": "", "Use 0.75 total daily dose. Reduced dose, e.g. for MDI patients": "", "Use default BG ranges": "", "Use full total daily dose": "", "Using Dexcom G5 Mobile on Android? See your data in Tidepool.": "", "UTC time: ": "", "Values": "", "Verify your account": "", "Veterinary Clinic": "", "View": "", "View and upload patient data": "", "View clinic members": "", "View Clinic Members": "", "View data for:": "", "View data from": "", "View Patient List": "", "View Patients that spend:": "", "View Prescription": "", "View, share and manage patient data": "", "W_Wednesday": "W", "Waiting for confirmation": "", "Waiting to import data": "", "We are calculating the basal rates here using the active basal schedule in your pump settings (and applying the percentage of an active temp basal where applicable), but {{source}} did not directly provide us with these rate changes.": "", "We are deriving the basal rate to display here assuming that your pump resumed to the basal that was active before the pump was suspended, but {{source}} did not directly provide us with this rate change.": "", "We are estimating the duration of the basal rate here using the basal schedule active at download, but {{source}} did not directly provide us with this information.": "", "We are really sorry, but you need to be 13 or older in order to create an account and use Tidepool's Applications.": "", "We can't be 100% certain of the data displayed here because of how ": "", "We can't be 100% certain of the data displayed here.": "", "We couldn't change your password. You may have mistyped your email, or the reset link may have expired.": "", "We have estimated the duration of this override; it was not provided directly by {{source}}": "", "We have fabricated this basal segment from a {{source}} new day event; it was not provided directly as basal data": "", "We just sent you an e-mail.": "", "We know this bolus was canceled, but {{source}} pumps do not capture exactly when.": "", "We were unable to find a clinic with that share code.": "", "We were unable to log this error to our server so could you please send us a note at <a style=\"text-decoration: underline;\" href=\"mailto:<EMAIL>\"><EMAIL></a> and we'll try to see what broke?": "", "Website": "", "week": "", "week_timeago": "week", "weeks": "", "weeks_timeago": "", "Weight": "", "Weight is required": "", "Weight:": "", "Welcome To Tidepool": "", "Welcome!": "", "What is {{patientName}}'s email address?": "", "What is {{patientName}}'s gender?": "", "What is {{patientName}}'s Medical Record Number?": "", "What is {{patientName}}'s parent/guardian's name and email address?": "", "What is the mobile phone number {{patientName}} will use with Tidepool Loop?": "", "What is the type of organization you are a part of?": "", "What is this?": "", "When your glucose is predicted to go below this value, the app will recommend a basal rate of 0 U/h and will not recommend a bolus.": "", "Which kind of account do you need?": "", "Who are you creating an account for?": "", "Whoa, sorry about that. Looks like Tidepool needs to change the battery on its pump.": "", "Whoops! Tidepool ran out of test strips...": "", "Whoops! Tidepool's servers got clogged with glucose tabs.": "", "Why is this grey?": "", "Why is this grey? There is not enough data to show this statistic.": "", "With diabetes, any reading in range is hard to achieve! If you don’t wear a CGM or don’t have enough CGM data, this shows how many of your fingerstick readings were in range, which can help you see how you are doing overall. We will only show a number if there is enough data - at least 4 readings in the one day view, and at least 4 readings for at least half of the days shown in the two week view.": "", "With diabetes, any time in range is hard to achieve! This shows the percentage of time your CGM was in range, which can help you see how you are doing overall. We will only show a number if there is enough data - readings for at least 75% of the day in the one day view, and 75% of the day for at least half of the days shown in the two week view.": "", "With your Clinic account, you will be able to:": "", "Within this basal segment, we are omitting a suspend event that didn't end. This may have resulted from changing the date & time settings on the device or switching to a new device. As a result, this basal segment may be inaccurate.": "", "Workout Correction Range": "", "Would you like to set up data storage for yourself or for someone else's diabetes data?": "", "Wrong username or password.": "", "year": "", "Year": "", "year_timeago": "year", "years": "", "years_timeago": "years", "Yes": "", "Yes, let's set it up": "", "Yes, Patient requires in-person CPT training": "", "Yesterday": "", "You are a doctor, a clinic or other healthcare provider that wants to use Tidepool to help people in your care.": "", "You can add up to {{maxClinicPatientTags}} tags per clinic": "", "You can choose how Tidepool Loop measures the insulin’s peak activity according to one of these two insulin models that you’ll select now.": "", "You have been invited to see {{name}}'s data!": "", "You have invited the following members to view your data:": "", "You have left {{name}}.": "", "You have not entered a password.": "", "You have not invited any other members to view your data.": "", "You have not specified a valid birthday!": "", "You have not specified your birthday!": "", "You have selected Tidepool Loop in-app tutorial self start. A request will not be sent for this patient to receive CPT training.": "", "You have successfully {{messageAction}} a Tidepool Loop prescription.": "", "You have successfully added a new patient.": "", "You have successfully updated a patient.": "", "You have successfully updated the clinician.": "", "You may have changed pumps recently - perhaps because you had to have your pump replaced due to malfuction. As a result of how {{source}} reports the data, we can't be 100% certain of your basal rate here.": "", "You must acknowledge admin role": "", "You must be at least 13 years old.": "", "You want to manage your diabetes data. You are caring for or supporting someone with diabetes.": "", "You will be logged out of the system upon confirming. You need to login again into your Tidepool account to continue to the new clinic workspace.": "", "You will lose all access to {{name}} and its patient list. Are you sure you want to leave this clinic?": "", "You're in. You now have access to {{name}}.": "", "You've been invited to Tidepool.": "", "Your basal rate of insulin is the number of units per hour that you want to use to cover your background insulin needs.": "", "Your carb ratio is the number of grams of carbohydrate covered by one unit of insulin.": "", "Your insulin sensitivity factor (ISF) is the {{bgUnits}} drop in glucose expected from one unit of insulin.": "", "Your password was changed successfully. You can now log in with your new password.": "", "Zip/Postal Code": ""}